#include <stdio.h>
#include <time.h>
#include <limits.h>
#include <stdbool.h>

#define ROWS 10
#define COLS 25
#define FLOORS 3
#define CELLS FLOORS*ROWS*COLS

typedef enum {
    NORMAL,
    START,
    BAWANA,
    DISABLED,
    WALL
} CellType;

typedef enum {
    NORTH,
    EAST,
    SOUTH,
    WEST
} Direction;

typedef enum {
    BIDIRECTION,
    UP,
    DOWN
} StairDir;

typedef enum {
    HAPPY,
    FOOD_P,
    POINTS,
    TRIGGERED,
    DISORIENTED
} Bw_Type;

typedef struct {
    int s_floor, s_row, s_col, e_floor, e_row, e_col;
    Direction dir;
} Stair;

typedef struct {
    int s_floor, e_floor, row, col;
} Pole;

typedef struct {
    CellType type;
    Stair *stair[2];
    Pole *pole;
    int floor, row, col;
    int stair_num;
    int cost;
    bool mul;
    Bw_Type bw_type;
    int bw_mp;
}Cell;

typedef struct {
    char name;
    int floor, row, col;
    int  s_row, s_col;
    int  init_row, init_col;
    Direction direction;
    int in_board;
    Direction start_direction;
    int left_to_dir_change;
    int skips;
    bool triggered;
    int mp;
    int dis_left;
} Player;

typedef struct {
    int floor, s_row,s_col, e_row, e_col;
} Wall;


// define main globals
Cell BOARD[FLOORS][ROWS][COLS];
Player PLAYERS[3];
Stair STAIRS[CELLS];
Wall WALLS[CELLS];
Pole POLES[CELLS];

int walls_num=0;
int stairs_num=0;
int poles_num = 0;



void init_floors() {
    for (int f=0; f<FLOORS; f++) {
        for (int r=0; r<ROWS;r++) {
            for (int c=0; c<COLS; c++) {
                BOARD[f][r][c].type = NORMAL;
                if (f == 0) {
                    if (BOARD[f][r][c].col >= 8 && BOARD[f][r][c].col <= 16 && BOARD[f][r][c].row >= 6 && BOARD[f][r][c].row <= 9){
                        BOARD[f][r][c].type = START;
                    }
                // bawana later
                    if (BOARD[f][r][c].col >= 21 && BOARD[f][r][c].col <= 24 && BOARD[f][r][c].row >= 7 && BOARD[f][r][c].row <= 9){
                        BOARD[f][r][c].type = BAWANA;
                    }
                    if ((BOARD[f][r][c].col==20 && BOARD[f][r][c].row >= 6) || (BOARD[f][r][c].row == 6 && BOARD[f][r][c].col >= 20 )){
                        BOARD[f][r][c].type = WALL;
                    }
                }
                if (f == 1){
                    if ((BOARD[f][r][c].col >= 8 || BOARD[f][r][c].col <= 16) && !(BOARD[f][r][c].row >= 6)){
                        BOARD[f][r][c].type=DISABLED;
                    }
                }
                if ( f==2){
                    if (!(BOARD[f][r][c].col >= 8 && BOARD[f][r][c].col <=16)){
                        BOARD[f][r][c].type = DISABLED;
                    }
                }

            }
        }
    }
}


void load_stairs(){
    FILE *file = fopen("stairs.txt", "r");
    if (!file)
    {
        perror("Error opening stairs.txt");
        exit(1);
    }

    int count = 0;
    int s_floor, s_row, s_col,e_floor, e_row, e_col;
    while (fscanf(file, " [%d , %d , %d , %d , %d, %d] ", &s_floor, &s_row, &s_col, &e_floor, &e_row, &e_col) == 6){
        STAIRS[count].s_floor = s_floor;
        STAIRS[count].s_row = s_row;
        STAIRS[count].s_col = e_col;
        STAIRS[count].e_floor = e_floor;
        STAIRS[count].e_row = e_row;
        STAIRS[count].e_col = e_col;
        STAIRS[count].dir = BIDIRECTION;
        stairs_num=++count;
        }
}

void load_walls(){
    FILE *file = fopen("walls.txt", "r");
    if (!file)
    {
        perror("Error opening walls.txt");
        exit(1);
    }

    int count = 0;
    int floor, s_row, s_col, e_row, e_col;
    while (fscanf(file, " [%d , %d , %d , %d , %d] ", &floor, &s_row, &s_col, &e_row, &e_col) == 5){
        if (!(s_col == e_col || s_row == e_row)) {
            continue;
        }
        WALLS[walls_num].floor = floor;
        WALLS[walls_num].s_row = s_row;
        WALLS[walls_num].s_col = e_col;
        WALLS[walls_num].e_row = e_row;
        WALLS[walls_num].e_col = e_col;
        walls_num++;
        }
}
void load_poles(){
    FILE *file = fopen("poles.txt", "r");
    if (!file)
    {
        perror("Error opening poles.txt");
        exit(1);
    }

    int count = 0;
    int s_floor,e_floor, row, col;
    while (fscanf(file, " [%d , %d , %d , %d] ", &s_floor,e_floor, row, col) == 4){
        POLES[poles_num].s_floor = s_floor;
        POLES[poles_num].e_floor = e_floor;
        POLES[poles_num].row = row;
        POLES[poles_num].col = col;
        poles_num++;
        }
}


Cell *get_cell(int floor, int row, int col){
    if (row > 9 || col > 24 || col < 0 || row < 0 || floor > FLOORS) return NULL;
    return &BOARD[floor][row][col];
}

int check_cell(Cell *cell){
    if (!cell) return 0;
    if (cell->row > 9 || cell->col > 24 || cell->col < 0 || cell->row < 0) return 0;
    if (!cell->type== NORMAL) return 0;
    return 1;
}

void init_stairs() {
    for (int i = 0; i < stairs_num; i++) {
        Cell *end_cell = get_cell(STAIRS[i].e_floor, STAIRS[i].e_row, STAIRS[i].e_col);
        Cell *start_cell = get_cell(STAIRS[i].s_floor, STAIRS[i].s_row, STAIRS[i].s_col);
        if (!start_cell || !end_cell || !check_cell(start_cell) || !check_cell(end_cell) || start_cell->stair_num>2 || end_cell->stair_num>2) continue;
        start_cell->stair[start_cell->stair_num++]= &STAIRS[i];
        end_cell->stair[end_cell->stair_num++]= &STAIRS[i];
    }
}


void init_poles() {
    for (int i = 0; i < poles_num; i++) {
        Cell *end_cell = get_cell(POLES[i].e_floor, POLES[i].row, POLES[i].col);
        Cell *start_cell = get_cell(POLES[i].s_floor, POLES[i].row, POLES[i].col);
        if (!check_cell(start_cell) || !check_cell(end_cell) || !start_cell || !end_cell) continue;
        start_cell->pole = &POLES[i];
        end_cell->pole= &POLES[i];
        if (POLES[i].e_floor == 2 && POLES[i].s_floor == 0) {
            Cell *mid_cell = get_cell(1,POLES[i].row, POLES[i].col);
            if (!mid_cell || !check_cell(mid_cell)) continue; //todo: add poles to starting area
            mid_cell->pole=&POLES[i];
        }
                // todo: add rest
    }
}


void init_walls() {
    for (int i = 0; i < walls_num; i++) {
        int floor = WALLS[i].floor;
        int s_row = WALLS[i].s_row;
        int s_col = WALLS[i].s_col;
        int e_row = WALLS[i].e_row;
        int e_col = WALLS[i].e_col;
        
        if (s_row == e_row) {
            // horizontal wall
            int start_col = s_col < e_col ? s_col : e_col;
            int end_col = s_col > e_col ? s_col : e_col;
            for (int c = start_col; c <= end_col; c++) {
                Cell *cell = get_cell(floor, s_row, c);
                if (cell) {
                    cell->type = WALL;
                }
            }
        }
        if (s_col == e_col) {
            // vertical wall  
            int start_row = s_row < e_row ? s_row : e_row;
            int end_row = s_row > e_row ? s_row : e_row;
            for (int r = start_row; r <= end_row; r++) {
                Cell *cell = get_cell(floor, r, s_col);
                if (cell) {
                    cell->type = WALL;
                }
            }
        }
    }
}

int attempt_move(Player *P, int die_roll) {
    Cell *start_cell = get_cell(P->floor, P->row, P->col);
    Cell*current_cell = start_cell;
    int path_blocked = 0;
    int wall_block=0;
    int remaining = die_roll;
    while (remaining>0) {
        switch (P->direction){
            case NORTH:
                P->row--;
                break;
            case EAST:
                P->col++;
                break;
            case SOUTH:
                P->row++;
                break;
            case WEST:
                P->col--;
                break;
            default:
                break;
        }
        current_cell = get_cell(P->floor, P->row, P->col);
        if (!check_cell(current_cell)){
            P->floor=start_cell->floor;
            P->row=start_cell->row;
            P->col=start_cell->col;
            path_blocked = 1;
            if (current_cell && current_cell->type==WALL){
                wall_block=1;
            }
            break;
        }
        current_cell =  apply_poles(P,current_cell);
        current_cell =  apply_stairs(P,current_cell);
        apply_consumables(P, current_cell);
    }
    if (current_cell==start_cell){
        if (!path_blocked){
            //self capture
            P->direction=P->start_direction;
            P->in_board = 0;
            P->floor = 0;
            P->row = P->init_row;
            P->col = P->init_col;
            printf("Player %c stuck on a loop and sent to start position\n", P->name);
        }
    } else {
        
        P->floor = current_cell->floor;
        P->row = current_cell->row;
        P->col = current_cell->col;
        if (wall_block){
            P->mp -= 2;
            printf("%c moved 0 that cost 2 movement points and is left with %d and is moving in the %s.\n",
            P->name, P->mp, direction_name(P->direction));
            if (P->mp <= 0) {
                printf("%c movement points are depleted and transporting to Bawana.\n", P->name);
                bawana_effect(P);
            }
        }
    }
}

int man_dis(Cell *cell1, Cell *cell2){
    int dist = abs(cell1->row-cell2->row)+abs(cell1->col-cell2->col)+abs(cell1->floor-cell2->floor);
    return dist*(abs(cell1->floor-cell2->floor)+1);
}

Cell *apply_stairs(Player *P, Cell *current_cell){
    if (!current_cell || current_cell->stair_num<=0) return current_cell;
    if (!current_cell->stair[0]) return current_cell;
    Cell *nxt_cell = current_cell;
    Cell *stair_start_cell = get_cell(current_cell->stair[0]->s_floor, current_cell->stair[0]->s_row, current_cell->stair[0]->s_col);
    Cell *stair_end_cell = get_cell(current_cell->stair[0]->e_floor, current_cell->stair[0]->e_row, current_cell->stair[0]->e_col);
    int best_dist=man_dis(stair_start_cell,stair_end_cell);
    for (int i=0; i<current_cell->stair_num;i++){
        Cell *start_cell = get_cell(current_cell->stair[i]->s_floor, current_cell->stair[i]->s_row, current_cell->stair[i]->s_col);
        Cell *end_cell = get_cell(current_cell->stair[i]->s_floor, current_cell->stair[i]->s_row, current_cell->stair[i]->s_col);
        if (man_dis(start_cell,end_cell) < best_dist){
            best_dist = man_dis(start_cell,end_cell);
            stair_start_cell=start_cell;
            stair_end_cell=end_cell;
        }
    }
    if ((current_cell->stair[0]->dir == UP  || current_cell->stair[0]->dir == BIDIRECTION ) && current_cell==stair_start_cell){
        if (check_cell(stair_end_cell)){
            nxt_cell = stair_end_cell;
        }
    } else if ((current_cell->stair[0]->dir == DOWN || current_cell->stair[0]->dir == BIDIRECTION) && current_cell==stair_end_cell) {
        if (check_cell(stair_start_cell)) {
            nxt_cell = stair_start_cell;
        }
    }
    return nxt_cell;
}
Cell *apply_poles(Player *P, Cell *current_cell){
    if (!(current_cell)) return current_cell;
    Cell *nxt_cell = current_cell;
    Cell *pole_start_cell = get_cell(current_cell->pole->s_floor, current_cell->pole->row, current_cell->pole->col);
    Cell *pole_end_cell = get_cell(current_cell->pole->e_floor, current_cell->pole->row, current_cell->pole->col);
    if (current_cell->pole && current_cell->floor<=pole_end_cell->floor && current_cell->row==pole_end_cell->row && current_cell->col==pole_end_cell->col){
        if (check_cell(pole_start_cell)){
            nxt_cell = pole_start_cell;
        }
    }
    return nxt_cell;
}

int rand_int(int a, int b) {
    return a+(rand()%(abs(a-b)+1));
}

void initialize_consumables(){
    int cell_count=0;
    int normal_cells=0;
    for (int f = 0;  f < FLOORS; f++) {
        for (int r=0; r  < ROWS; r++) {
            for (int c=0; c < COLS; c++) {
                Cell *cell = get_cell(f,r,c);
                if (cell && check_cell(cell)){
                    normal_cells++;
                }
            }
        }
    }
    int c_0_a, b_12_a, c_1_a, b_35_a, mul_23_a;
    int c_0 = (int) (0.25*normal_cells);
    int c_1 = (int) (0.35*normal_cells);
    int b_12 = (int) (0.25*normal_cells);
    int b_35 = (int) (0.10*normal_cells);
    int mul_23 = (int) (0.05*normal_cells);
    while (cell_count < normal_cells){
        Cell *random_cell = get_cell(rand_int(0,2),rand_int(0,9), rand_int(0,24));
        if (random_cell->type!=NORMAL || random_cell->cost==0) {
            if (c_1_a < c_1){
                random_cell->cost= -rand_int(1,4);
                cell_count++;
                c_1_a++;
            }
            else if (b_12_a < b_12){
                random_cell->cost=rand_int(1,2);
                cell_count++;
                b_12_a++;
            }
            else if (b_35_a < b_35){
                random_cell->cost=rand_int(3,5);
                cell_count++;
                b_35_a++;
            }

            else if (mul_23_a < mul_23){
                random_cell->cost=rand_int(2,3);
                random_cell->mul=true;
                cell_count++;
                mul_23_a++;
            }
            else if (c_0_a < c_0){
                random_cell->cost=0;
                cell_count++;
                c_0_a++;
            }
        }

    }
}

void initialize_bawana() {
    int i = 0;
    int added=0;

    while (i<2 && added<12)
    {
        Cell *random= get_cell(0,rand_int(5,9), rand_int(21,24));
        if (!random) continue;
        if (random->bw_type) continue;
        random->bw_type=FOOD_P;
    }
    while (i<2 && added<12)
    {
        Cell *random= get_cell(0,rand_int(5,9), rand_int(21,24));
        if (!random) continue;
        if (random->bw_type) continue;
        random->bw_type=DISORIENTED;
    }
    while (i<2 && added<12)
    {
        Cell *random= get_cell(0,rand_int(5,9), rand_int(21,24));
        if (!random) continue;
        if (random->bw_type) continue;
        random->bw_type=TRIGGERED;
    }
    while (i<4 && added<12)
    {
        Cell *random= get_cell(0,rand_int(5,9), rand_int(21,24));
        if (!random) continue;
        if (random->bw_type) continue;
        random->bw_type=POINTS;
        random->bw_mp=rand_int(10,100);
    }
    
}

void bawana_effect(Player *p) {
    Cell *b_cell = get_cell(0,rand_int(5,9), rand_int(21,24));
    if (!b_cell) return;
    Bw_Type type = b_cell->bw_type;
    switch (type)
    {
    case FOOD_P:
        p->skips=3;
        printf("%c Eats from bawana. get food poisoning and 3 tuurns are set to be skipped\n", p->name);
        break;
    case TRIGGERED:
        p->triggered=true;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->mp += 50;
        p->direction=NORTH;
        printf("%c Eats from bawana. get triggered and exits from bawana\n", p->name);
        break;
    case DISORIENTED:
        p->dis_left=4;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->mp += 50;
        p->direction=NORTH;
        printf("%c Eats from bawana. get disoriented and 4 turns are set to be random directional\n", p->name);
        break;
    case POINTS:
        p->mp += b_cell->bw_mp;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->direction=NORTH;
        printf("%c Eats from bawana. get points and exits bawana\n", p->name);
        break;
    case HAPPY:
        p->mp += 200;
        p->floor=0;
        p->row=9;
        p->col=19;
        p->direction=NORTH;
        printf("%c Eats from bawana. get happy get 200 mp and exits bawana\n", p->name);
        break;
    
    default:
        break;
    }
}


void apply_consumables(Player *p, Cell *cell){
    if (cell == NULL) return;  
    if (p->mp >= 250) {
        p->mp=250;
        return;
    }
    if (cell->cost > 0) {
        if (cell->mul) {
            p->mp *= cell->cost;
        } else {
            p->mp += cell->cost;
        }
        if (p->mp >= 250) {
            p->mp=250;
        }
    }
}


//todo: write all initializations in initialize_game function
void initialize_game() {

}

// todo: write a play function to play the game. all game loops and everything is in this function
// 1000 rounds use above helpers


int main() {
    srand(time(NULL));
    // execute game from here
}


// use codes in inspire.c file to get inspiration. do not chnage quality of above codes they are there for a reason
// get inspiration add the 3 functions and make the game work
// printing to standard output is expected no need of logging
